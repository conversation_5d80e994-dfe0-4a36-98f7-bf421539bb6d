"""
شريط الحالة المحفوظ مع التصميم والألوان
تم حفظ التصميم الأصلي وإزالة الأكواد غير المستخدمة
"""

from PyQt5.QtWidgets import QStatusBar, QLabel, QPushButton
from PyQt5.QtCore import QTimer
import datetime


class SavedStatusBar:
    """فئة شريط الحالة المحفوظ"""

    def __init__(self, main_window):
        self.main_window = main_window
        self.statusBar = None

    def create_status_bar(self):
        """إنشاء شريط الحالة المحفوظ مع التصميم والألوان"""
        # إنشاء شريط الحالة
        self.statusBar = QStatusBar()
        self.main_window.setStatusBar(self.statusBar)

        # استيراد الأنماط الموحدة
        from ui.unified_styles import UnifiedStyles
        colors = UnifiedStyles.COLORS
        border_radius = UnifiedStyles.BORDER_RADIUS

        # تطبيق النمط المحفوظ لشريط الحالة
        self.statusBar.setStyleSheet(f"""
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border-top: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.4), stop:0.5 rgba(255, 255, 255, 0.6), stop:1 rgba(255, 255, 255, 0.4));
                border-bottom: 2px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.3), stop:0.5 rgba(255, 255, 255, 0.5), stop:1 rgba(255, 255, 255, 0.3));
                padding: 2px 5px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: {UnifiedStyles.FONT_SIZES['small']};
                font-weight: {UnifiedStyles.FONT_WEIGHTS['semibold']};
                color: {colors['text_inverse']};
                min-height: 35px;
                max-height: 35px;
            }}
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.2 rgba(255, 255, 255, 0.12),
                    stop:0.4 rgba(255, 255, 255, 0.15),
                    stop:0.6 rgba(255, 255, 255, 0.12),
                    stop:0.8 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(255, 255, 255, 0.05));
                color: #ffffff;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                font-weight: 700;
                font-size: 16px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 4px 20px;
                margin: 2px;
                min-height: 26px;
                max-height: 26px;
                text-align: center;
                qproperty-alignment: AlignCenter;
                vertical-align: middle;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.2 rgba(255, 255, 255, 0.12),
                    stop:0.4 rgba(255, 255, 255, 0.15),
                    stop:0.6 rgba(255, 255, 255, 0.12),
                    stop:0.8 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(255, 255, 255, 0.05));
                color: #ffffff;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                font-weight: 700;
                font-size: 16px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 4px 20px;
                min-width: 55px;
                max-width: 60px;
                min-height: 26px;
                max-height: 26px;
                transition: all 0.3s ease;

            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25),
                    stop:0.2 rgba(255, 255, 255, 0.30),
                    stop:0.4 rgba(255, 255, 255, 0.35),
                    stop:0.6 rgba(255, 255, 255, 0.30),
                    stop:0.8 rgba(255, 255, 255, 0.25),
                    stop:1 rgba(255, 255, 255, 0.20));
                border: 2px solid rgba(255, 255, 255, 0.5);
                transform: translateY(-1px);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(255, 255, 255, 0.3),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }}
            QPushButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.6),
                    stop:0.2 rgba(59, 130, 246, 0.5),
                    stop:0.4 rgba(96, 165, 250, 0.4),
                    stop:0.6 rgba(139, 92, 246, 0.5),
                    stop:0.8 rgba(124, 58, 237, 0.6),
                    stop:1 rgba(109, 40, 217, 0.5));
                border: 2px solid rgba(96, 165, 250, 0.7);
                box-shadow:
                    inset 0 2px 4px rgba(0, 0, 0, 0.2),
                    0 0 12px rgba(96, 165, 250, 0.3),
                    0 0 20px rgba(139, 92, 246, 0.2);
            }}
        """)

        # إضافة مؤشر اسم القسم الحالي على اليسار
        self.create_current_section_indicator()

        # إضافة المميزات المتطورة من اليمين بالترتيب
        self.create_advanced_right_features()



        # إنشاء مؤقت التحديث المتطور
        self.setup_advanced_timer()

        print("✅ تم إنشاء شريط الحالة المحفوظ بنجاح")

    def create_current_section_indicator(self):
        """📍 إنشاء مؤشر القسم الحالي على اليسار"""
        try:
            # إنشاء مؤشر اسم القسم الحالي مع أيقونة ثابتة للوحة المعلومات
            self.main_window.status_message_label = QLabel("📊 القسم الحالي: لوحة المعلومات")
            self.main_window.status_message_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.18),
                        stop:0.15 rgba(255, 255, 255, 0.22),
                        stop:0.3 rgba(255, 255, 255, 0.25),
                        stop:0.5 rgba(255, 255, 255, 0.28),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:0.85 rgba(255, 255, 255, 0.22),
                        stop:1 rgba(255, 255, 255, 0.18));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 900;
                    font-size: 22px;
                    border: 2px solid rgba(255, 255, 255, 0.35);
                    border-radius: 15px;
                    padding: 4px 25px;
                    min-width: 300px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                    box-shadow:
                        0 0 25px rgba(255, 255, 255, 0.15),
                        0 4px 15px rgba(0, 0, 0, 0.2),
                        inset 0 2px 4px rgba(255, 255, 255, 0.2),
                        inset 0 -2px 4px rgba(0, 0, 0, 0.08);
                    text-shadow:
                        0 1px 3px rgba(0, 0, 0, 0.4),
                        0 0 15px rgba(255, 255, 255, 0.2);
                    animation: glow 3s ease-in-out infinite alternate;
                }
                @keyframes glow {
                    from { box-shadow:
                        0 0 20px rgba(255, 255, 255, 0.12),
                        0 4px 15px rgba(0, 0, 0, 0.2),
                        inset 0 2px 4px rgba(255, 255, 255, 0.2),
                        inset 0 -2px 4px rgba(0, 0, 0, 0.08); }
                    to { box-shadow:
                        0 0 30px rgba(255, 255, 255, 0.18),
                        0 4px 15px rgba(0, 0, 0, 0.2),
                        inset 0 2px 4px rgba(255, 255, 255, 0.2),
                        inset 0 -2px 4px rgba(0, 0, 0, 0.08); }
                }
            """)
            self.main_window.status_message_label.setToolTip("📍 يعرض اسم القسم أو الصفحة الحالية\n• يتغير تلقائياً عند التنقل\n• الأيقونة تتغير حسب القسم\n• يساعد في معرفة موقعك الحالي")

            # إضافة المؤشر إلى بداية شريط الحالة (اليسار)
            self.statusBar.addWidget(self.main_window.status_message_label)

            print("✅ تم إنشاء مؤشر القسم الحالي بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء مؤشر القسم الحالي: {str(e)}")

    def create_advanced_right_features(self):
        """🚀 إنشاء المميزات المتطورة جداً من اليمين بالترتيب المعكوس"""
        try:
            # 1️⃣ أزرار التحكم السريع (الأول من اليمين - معكوس)
            self.create_control_buttons()

            # 2️⃣ مؤشر الشبكة والاتصال (الثاني من اليمين - معكوس)
            self.network_status = QLabel("🚀 الشبكة: متصل")
            self.network_status.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.2 rgba(255, 255, 255, 0.12),
                        stop:0.4 rgba(255, 255, 255, 0.15),
                        stop:0.6 rgba(255, 255, 255, 0.12),
                        stop:0.8 rgba(255, 255, 255, 0.08),
                        stop:1 rgba(255, 255, 255, 0.05));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 15px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.network_status.setToolTip("🌐 حالة الشبكة والاتصال\n• سرعة الإنترنت\n• استقرار الاتصال\n• جودة الإشارة")
            self.statusBar.addPermanentWidget(self.network_status)

            # 3️⃣ مؤشر الأمان المتقدم (الثالث من اليمين - معكوس)
            self.security_status = QLabel("🛡️ الأمان: محمي")
            self.security_status.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.2 rgba(255, 255, 255, 0.12),
                        stop:0.4 rgba(255, 255, 255, 0.15),
                        stop:0.6 rgba(255, 255, 255, 0.12),
                        stop:0.8 rgba(255, 255, 255, 0.08),
                        stop:1 rgba(255, 255, 255, 0.05));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: none;
                    border-radius: 15px;
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.security_status.setToolTip("🔒 مؤشر الأمان المتقدم\n• حماية البيانات\n• تشفير متقدم\n• مراقبة الوصول")
            self.statusBar.addPermanentWidget(self.security_status)

            # 4️⃣ مؤشر البيانات المباشر (الرابع من اليمين - معكوس)
            self.live_data_status = QLabel("💾 البيانات: محدثة")
            self.live_data_status.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:0.2 rgba(255, 255, 255, 0.20),
                        stop:0.4 rgba(255, 255, 255, 0.25),
                        stop:0.6 rgba(255, 255, 255, 0.20),
                        stop:0.8 rgba(255, 255, 255, 0.15),
                        stop:1 rgba(255, 255, 255, 0.10));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 15px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.live_data_status.setToolTip("📊 حالة البيانات المباشرة\n• تحديث فوري\n• مزامنة تلقائية\n• دقة عالية")
            self.statusBar.addPermanentWidget(self.live_data_status)

            # 5️⃣ مؤشر الأداء الذكي (الخامس من اليمين - معكوس)
            self.smart_performance = QLabel("🔥 الأداء: ممتاز")
            self.smart_performance.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:0.2 rgba(255, 255, 255, 0.20),
                        stop:0.4 rgba(255, 255, 255, 0.25),
                        stop:0.6 rgba(255, 255, 255, 0.20),
                        stop:0.8 rgba(255, 255, 255, 0.15),
                        stop:1 rgba(255, 255, 255, 0.10));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 15px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.smart_performance.setToolTip("🔥 مؤشر الأداء الذكي\n• مراقبة المعالج\n• استخدام الذاكرة\n• سرعة قاعدة البيانات")
            self.statusBar.addPermanentWidget(self.smart_performance)

            # 6️⃣ مؤشر الوقت والتاريخ المتطور (السادس من اليمين - معكوس)
            self.datetime_indicator = QLabel("⏰ جاري التحميل...")
            self.datetime_indicator.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:0.2 rgba(255, 255, 255, 0.20),
                        stop:0.4 rgba(255, 255, 255, 0.25),
                        stop:0.6 rgba(255, 255, 255, 0.20),
                        stop:0.8 rgba(255, 255, 255, 0.15),
                        stop:1 rgba(255, 255, 255, 0.10));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 15px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.datetime_indicator.setToolTip("🕐 الساعة الرقمية المتحركة\n• نظام 12 ساعة\n• تحديث تلقائي كل ثانية\n• حركة بصرية متطورة")
            self.statusBar.addPermanentWidget(self.datetime_indicator)

            print("✅ تم إنشاء المميزات المتطورة من اليمين بالترتيب المعكوس بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء المميزات المتطورة: {str(e)}")

    def create_control_buttons(self):
        """🔧 إنشاء أزرار التحكم السريع المتطورة"""
        try:
            # نمط الأزرار المطور ليتشابه مع شريط العنوان
            button_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:0.2 rgba(255, 255, 255, 0.20),
                        stop:0.4 rgba(255, 255, 255, 0.25),
                        stop:0.6 rgba(255, 255, 255, 0.20),
                        stop:0.8 rgba(255, 255, 255, 0.15),
                        stop:1 rgba(255, 255, 255, 0.10));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 20px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 15px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    transition: all 0.3s ease;
                    padding: 4px 20px;
                    margin: 2px;
                    min-width: 50px;
                    max-width: 55px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.2 rgba(255, 255, 255, 0.30),
                        stop:0.4 rgba(255, 255, 255, 0.35),
                        stop:0.6 rgba(255, 255, 255, 0.30),
                        stop:0.8 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.20));
                    border: 2px solid rgba(255, 255, 255, 0.5);
                    transform: translateY(-1px);
                    box-shadow:
                        0 4px 12px rgba(0, 0, 0, 0.25),
                        0 0 15px rgba(255, 255, 255, 0.3),
                        inset 0 1px 2px rgba(255, 255, 255, 0.3);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    transform: translateY(1px);
                    box-shadow:
                        0 0 15px rgba(96, 165, 250, 0.4),
                        0 2px 8px rgba(0, 0, 0, 0.4),
                        inset 0 1px 2px rgba(255, 255, 255, 0.3);
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.6),
                        stop:0.2 rgba(59, 130, 246, 0.5),
                        stop:0.4 rgba(96, 165, 250, 0.4),
                        stop:0.6 rgba(139, 92, 246, 0.5),
                        stop:0.8 rgba(124, 58, 237, 0.6),
                        stop:1 rgba(109, 40, 217, 0.5));
                    border: 2px solid rgba(96, 165, 250, 0.7);
                    box-shadow:
                        inset 0 2px 4px rgba(0, 0, 0, 0.2),
                        0 0 12px rgba(96, 165, 250, 0.3),
                        0 0 20px rgba(139, 92, 246, 0.2);
                }
            """

            # 🔄 زر التحديث الفوري
            self.refresh_btn = QPushButton("🔃")
            self.refresh_btn.setStyleSheet(button_style)
            self.refresh_btn.setToolTip("🔄 تحديث فوري لجميع البيانات")
            self.refresh_btn.clicked.connect(self.instant_refresh)
            self.statusBar.addPermanentWidget(self.refresh_btn)

            # 🔔 زر الإشعارات
            self.notifications_btn = QPushButton("🔊")
            self.notifications_btn.setCheckable(True)
            self.notifications_btn.setChecked(True)
            self.notifications_btn.setStyleSheet(button_style)
            self.notifications_btn.setToolTip("🔔 تبديل الإشعارات")
            self.notifications_btn.clicked.connect(self.toggle_notifications)
            self.statusBar.addPermanentWidget(self.notifications_btn)

            # ⚙️ زر الإعدادات السريعة
            self.settings_btn = QPushButton("⚙️")
            self.settings_btn.setStyleSheet(button_style)
            self.settings_btn.setToolTip("⚙️ الإعدادات السريعة")
            self.settings_btn.clicked.connect(self.open_main_settings)
            self.statusBar.addPermanentWidget(self.settings_btn)

            # 🌟 زر الوضع المتقدم
            self.advanced_mode_btn = QPushButton("🌟")
            self.advanced_mode_btn.setCheckable(True)
            self.advanced_mode_btn.setStyleSheet(button_style)
            self.advanced_mode_btn.setToolTip("🌟 تبديل الوضع المتقدم")
            self.advanced_mode_btn.clicked.connect(self.toggle_advanced_mode)
            self.statusBar.addPermanentWidget(self.advanced_mode_btn)

        except Exception as e:
            print(f"❌ خطأ في إنشاء أزرار التحكم: {str(e)}")

    def setup_advanced_timer(self):
        """⏰ إعداد مؤقت التحديث المتطور"""
        try:
            # مؤقت التحديث الرئيسي (تحديث محسن للشبكة والأمان)
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_all_features)
            self.update_timer.start(10000)  # تحديث كل 10 ثوان

            # مؤقت الوقت والتاريخ (تحديث كل ثانية للحركة التلقائية)
            self.datetime_timer = QTimer()
            self.datetime_timer.timeout.connect(self.update_datetime)
            self.datetime_timer.start(1000)  # تحديث كل ثانية واحدة

            # مؤقت منفصل لفحص الشبكة (كل 10 ثوان)
            self.network_timer = QTimer()
            self.network_timer.timeout.connect(self.update_network_status)
            self.network_timer.start(10000)  # تحديث كل 10 ثوان

            # تحديث فوري للوقت والشبكة
            self.update_datetime()
            self.update_network_status()  # فحص فوري للشبكة

            # تأخير التحديث الأول للمميزات الأخرى لتجنب الوميض
            QTimer.singleShot(5000, self.update_all_features)  # 5 ثوان

        except Exception as e:
            print(f"❌ خطأ في إعداد المؤقتات: {str(e)}")

    def update_status_info(self):
        """تحديث معلومات شريط الحالة - للتوافق مع الكود القديم"""
        try:
            self.update_all_features()
        except Exception as e:
            print(f"خطأ في تحديث معلومات الحالة: {str(e)}")

    def update_all_features(self):
        """🔄 تحديث جميع المميزات المتطورة مع بيانات حقيقية"""
        try:
            import random
            import psutil
            import time

            # تحديث مؤشر الأداء الذكي مع بيانات حقيقية
            try:
                cpu_percent = psutil.cpu_percent(interval=0.1)
                memory_percent = psutil.virtual_memory().percent

                if cpu_percent < 30 and memory_percent < 70:
                    performance_msg = "🔥 الأداء: ممتاز"
                elif cpu_percent < 60 and memory_percent < 85:
                    performance_msg = "⚡ الأداء: جيد"
                else:
                    performance_msg = "⚠️ الأداء: متوسط"

                self.smart_performance.setText(performance_msg)
                self.smart_performance.setToolTip(f"🔥 مؤشر الأداء الذكي\n• المعالج: {cpu_percent:.1f}%\n• الذاكرة: {memory_percent:.1f}%\n• الحالة: {'ممتاز' if cpu_percent < 30 else 'جيد' if cpu_percent < 60 else 'متوسط'}")

            except ImportError:
                # إذا لم يكن psutil متاحاً، استخدم رسائل عشوائية
                performance_messages = [
                    "🔥 الأداء: ممتاز",
                    "🔥 السرعة: عالية جداً",
                    "🔥 الاستجابة: فورية",
                    "🔥 الكفاءة: مثلى",
                    "🔥 النظام: محسن"
                ]
                self.smart_performance.setText(random.choice(performance_messages))

            # تحديث مؤشر البيانات المباشر مع إحصائيات حقيقية
            try:
                if hasattr(self.main_window, 'session') and self.main_window.session:
                    # فحص اتصال قاعدة البيانات والحصول على إحصائيات
                    session = self.main_window.session

                    # إحصائيات سريعة
                    stats = self.get_database_stats(session)

                    if stats:
                        total_records = stats.get('total_records', 0)
                        self.live_data_status.setText(f"📊 البيانات: {total_records} سجل")

                        # تحديث التلميح مع تفاصيل الإحصائيات
                        tooltip_text = (
                            "📊 إحصائيات قاعدة البيانات:\n"
                            f"• العملاء: {stats.get('clients', 0)}\n"
                            f"• الموردين: {stats.get('suppliers', 0)}\n"
                            f"• الموظفين: {stats.get('employees', 0)}\n"
                            f"• المشاريع: {stats.get('projects', 0)}\n"
                            f"• إجمالي السجلات: {total_records}\n"
                            f"• آخر تحديث: {datetime.datetime.now().strftime('%H:%M:%S')}"
                        )
                        self.live_data_status.setToolTip(tooltip_text)
                    else:
                        self.live_data_status.setText("📊 البيانات: متصلة")
                        self.live_data_status.setToolTip("📊 حالة البيانات المباشرة\n• الاتصال: نشط\n• قاعدة البيانات: متاحة\n• المزامنة: تلقائية")
                else:
                    self.live_data_status.setText("📊 البيانات: غير متصلة")
            except Exception as db_error:
                print(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {db_error}")
                data_messages = [
                    "📊 البيانات: محدثة",
                    "📊 المزامنة: مكتملة",
                    "📊 الاتصال: مستقر",
                    "📊 التحديث: تلقائي",
                    "📊 الحالة: نشطة"
                ]
                self.live_data_status.setText(random.choice(data_messages))

            # تحديث مؤشر الأمان مع فحص شامل
            try:
                security_info = self.check_security_status()

                if security_info['level'] == 'high':
                    security_msg = "🔒 الأمان: ممتاز"
                    security_color = "#10b981"  # أخضر
                elif security_info['level'] == 'medium':
                    security_msg = "🔐 الأمان: جيد"
                    security_color = "#f59e0b"  # أصفر
                else:
                    security_msg = "⚠️ الأمان: ضعيف"
                    security_color = "#ef4444"  # أحمر

                self.security_status.setText(security_msg)

                # تحديث التلميح مع تفاصيل الأمان
                security_tooltip = (
                    "🔒 تقرير الأمان المحلي:\n"
                    f"• قاعدة البيانات: {security_info['database']}\n"
                    f"• الملفات الأساسية: {security_info['files']}\n"
                    f"• سلامة البرنامج: {security_info.get('program_integrity', 'غير معروف')}\n"
                    f"• صلاحيات المستخدم: {security_info['user']}\n"
                    f"• حالة الشبكة: {security_info['network']} (للمعلومات)\n"
                    f"• مستوى الأمان المحلي: {security_info['level_text']}\n"
                    f"• آخر فحص: {datetime.datetime.now().strftime('%H:%M:%S')}\n"
                    f"• ملاحظة: الأمان لا يتأثر بحالة الإنترنت"
                )
                self.security_status.setToolTip(security_tooltip)

                # تغيير لون الحدود حسب مستوى الأمان
                current_style = self.security_status.styleSheet()
                if "border:" in current_style:
                    # استبدال لون الحدود
                    import re
                    new_style = re.sub(r'border: [^;]+;', f'border: 2px solid {security_color};', current_style)
                    self.security_status.setStyleSheet(new_style)

            except Exception as security_error:
                print(f"خطأ في فحص الأمان: {security_error}")
                security_messages = [
                    "🔒 الأمان: محمي",
                    "🔒 التشفير: نشط",
                    "🔒 الحماية: فعالة",
                    "🔒 الوصول: آمن",
                    "🔒 المراقبة: مستمرة"
                ]
                self.security_status.setText(random.choice(security_messages))

            # مؤشر الشبكة له مؤقت منفصل الآن - لا حاجة لتحديثه هنا

        except Exception as e:
            print(f"❌ خطأ في تحديث المميزات: {str(e)}")

    def update_network_status(self):
        """تحديث مؤشر الشبكة بشكل منفصل كل 10 ثوان"""
        try:
            import socket
            import time

            # فحص الاتصال بالإنترنت مع timeout أقل للاستجابة السريعة
            start_time = time.time()
            socket.create_connection(("8.8.8.8", 53), timeout=2)
            response_time = int((time.time() - start_time) * 1000)  # بالميلي ثانية

            # تحديد جودة الاتصال حسب سرعة الاستجابة
            if response_time < 100:
                speed_text = "ممتازة"
                speed_icon = "🚀"
            elif response_time < 300:
                speed_text = "جيدة"
                speed_icon = "⚡"
            else:
                speed_text = "بطيئة"
                speed_icon = "🐌"

            self.network_status.setText(f"🌐 الشبكة: متصل {speed_icon}")
            self.network_status.setToolTip(f"🌐 حالة الشبكة والاتصال\n• الإنترنت: متاح\n• الاتصال: مستقر\n• السرعة: {speed_text} ({response_time}ms)\n• آخر فحص: {time.strftime('%H:%M:%S')}\n• التحديث: كل 10 ثوان")

        except (socket.error, socket.timeout):
            import time
            self.network_status.setText("⚠️ الشبكة: غير متصل")
            self.network_status.setToolTip(f"⚠️ مشكلة في الشبكة\n• الإنترنت: غير متاح\n• يرجى التحقق من الاتصال\n• آخر محاولة: {time.strftime('%H:%M:%S')}\n• التحديث: كل 10 ثوان")

        except Exception as e:
            print(f"❌ خطأ في تحديث مؤشر الشبكة: {str(e)}")
            self.network_status.setText("❌ الشبكة: خطأ")
            self.network_status.setToolTip("❌ حدث خطأ في فحص الشبكة")

    def update_datetime(self):
        """🕐 تحديث الوقت والتاريخ المتطور مع نظام 12 ساعة"""
        try:
            from datetime import datetime

            # الحصول على الوقت الحالي
            now = datetime.now()

            # تحديد التحية حسب الوقت
            hour = now.hour
            if 6 <= hour < 12:
                greeting = "صباح الخير"
            elif 12 <= hour < 18:
                greeting = "نهارك سعيد"
            elif 18 <= hour < 22:
                greeting = "مساء الخير"
            else:
                greeting = "ليلة سعيدة"

            # تنسيق الوقت بنظام 12 ساعة
            hour_12 = now.hour
            am_pm = "ص"  # صباحاً

            if hour_12 == 0:
                hour_12 = 12
                am_pm = "ص"
            elif hour_12 < 12:
                am_pm = "ص"
            elif hour_12 == 12:
                am_pm = "م"  # مساءً
            else:
                hour_12 -= 12
                am_pm = "م"

            # تنسيق الوقت مع الثواني
            time_str = f"{hour_12:02d}:{now.minute:02d}:{now.second:02d} {am_pm}"

            # تنسيق التاريخ
            date_str = now.strftime("%Y/%m/%d")

            # أسماء الأيام بالعربية
            arabic_days = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
            day_name = arabic_days[now.weekday()]

            # إضافة حركة بصرية مبسطة
            # نقطة تومض كل ثانية
            blink_dot = "●" if now.second % 2 == 0 else "○"

            # مؤشر دوار للثواني (يدور كل 4 ثوان)
            rotation_chars = ["🕐", "🕑", "🕒", "🕓"]
            rotation_icon = rotation_chars[now.second % 4]

            # تحديث النص مع الحركة (مبسط لتوفير المساحة)
            display_text = f"{rotation_icon} {time_str} {blink_dot}"
            self.datetime_indicator.setText(display_text)

            # تحديث التلميح مع معلومات مفصلة ومعلومات النظام
            uptime_info = self.get_system_uptime()
            tooltip_text = (
                f"{greeting}\n"
                f"اليوم: {day_name}\n"
                f"التاريخ: {date_str}\n"
                f"الوقت (12 ساعة): {time_str}\n"
                f"الوقت (24 ساعة): {now.strftime('%H:%M:%S')}\n"
                f"المنطقة الزمنية: UTC+2\n"
                f"🕐 الساعة تتحرك تلقائياً كل ثانية\n"
                f"⏱️ وقت تشغيل البرنامج: {uptime_info}"
            )
            self.datetime_indicator.setToolTip(tooltip_text)

        except Exception as e:
            self.datetime_indicator.setText("🕐 خطأ في التوقيت")
            print(f"❌ خطأ في تحديث الوقت: {str(e)}")

    def instant_refresh(self):
        """🔄 تحديث فوري لجميع المميزات مع وظائف حقيقية"""
        try:
            # تأثير بصري للتحديث
            original_text = self.smart_performance.text()
            self.smart_performance.setText("🔄 جاري التحديث...")

            # تحديث جميع المميزات
            self.update_all_features()
            self.update_datetime()
            self.update_network_status()  # تحديث فوري للشبكة

            # تحديث بيانات النافذة الرئيسية إذا كانت متاحة
            if hasattr(self.main_window, 'refresh_current_section'):
                self.main_window.refresh_current_section()

            # تحديث القسم الحالي
            try:
                current_tab = self.main_window.tabs.currentWidget()
                if current_tab:
                    # محاولة تحديث البيانات بطرق مختلفة
                    if hasattr(current_tab, 'refresh_data'):
                        current_tab.refresh_data()
                    elif hasattr(current_tab, 'load_data'):
                        current_tab.load_data()
                    elif hasattr(current_tab, 'update_data'):
                        current_tab.update_data()
                    elif hasattr(current_tab, 'refresh'):
                        current_tab.refresh()

                    # تحديث الجداول إذا كانت موجودة
                    if hasattr(current_tab, 'table'):
                        current_tab.table.viewport().update()

                    print(f"✅ تم تحديث القسم الحالي: {self.main_window.tabs.tabText(self.main_window.tabs.currentIndex())}")

            except Exception as tab_error:
                print(f"خطأ في تحديث القسم الحالي: {tab_error}")

            # تحديث قاعدة البيانات
            if hasattr(self.main_window, 'session'):
                try:
                    self.main_window.session.commit()
                    self.live_data_status.setText("📊 البيانات: محدثة الآن")
                except Exception as db_error:
                    print(f"خطأ في تحديث قاعدة البيانات: {db_error}")

            # تحديث شريط الحالة المتقدم في النافذة الرئيسية
            if hasattr(self.main_window, 'update_advanced_status'):
                try:
                    self.main_window.update_advanced_status()
                except Exception as status_error:
                    print(f"خطأ في تحديث شريط الحالة المتقدم: {status_error}")

            # إعادة النص الأصلي بعد 3 ثوان
            QTimer.singleShot(3000, lambda: self.smart_performance.setText(original_text))

            print("✅ تم التحديث الفوري بنجاح مع تحديث البيانات")

        except Exception as e:
            print(f"❌ خطأ في التحديث الفوري: {str(e)}")

    def toggle_notifications(self):
        """🔔 تبديل الإشعارات مع وظائف حقيقية"""
        try:
            if self.notifications_btn.isChecked():
                # تفعيل الإشعارات
                self.live_data_status.setText("📊 الإشعارات: مفعلة")
                self.notifications_btn.setToolTip("🔔 إيقاف الإشعارات")
                self.notifications_btn.setText("🔊")

                # تفعيل الإشعارات في النافذة الرئيسية
                if hasattr(self.main_window, 'enable_notifications'):
                    self.main_window.enable_notifications()

                # إظهار رسالة تأكيد
                self.show_status_message("✅ تم تفعيل الإشعارات", 2000)

            else:
                # إيقاف الإشعارات
                self.live_data_status.setText("📊 الإشعارات: معطلة")
                self.notifications_btn.setToolTip("🔔 تشغيل الإشعارات")
                self.notifications_btn.setText("🔇")

                # إيقاف الإشعارات في النافذة الرئيسية
                if hasattr(self.main_window, 'disable_notifications'):
                    self.main_window.disable_notifications()

                # إظهار رسالة تأكيد
                self.show_status_message("⚠️ تم إيقاف الإشعارات", 2000)

            print(f"✅ تم تبديل الإشعارات: {'مفعلة' if self.notifications_btn.isChecked() else 'معطلة'}")

        except Exception as e:
            print(f"❌ خطأ في تبديل الإشعارات: {str(e)}")

    def quick_settings(self):
        """⚙️ الإعدادات السريعة مع نافذة حقيقية"""
        try:
            # تأثير بصري
            original_text = self.security_status.text()
            self.security_status.setText("⚙️ الإعدادات...")

            # فتح نافذة الإعدادات السريعة
            self.open_quick_settings_dialog()

            # إعادة النص الأصلي
            QTimer.singleShot(2000, lambda: self.security_status.setText(original_text))

            print("⚙️ تم فتح الإعدادات السريعة")

        except Exception as e:
            print(f"❌ خطأ في الإعدادات السريعة: {str(e)}")

    def open_quick_settings_dialog(self):
        """فتح نافذة الإعدادات السريعة"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QSlider, QLabel, QPushButton, QGroupBox
            from PyQt5.QtCore import Qt

            # إنشاء نافذة الإعدادات
            dialog = QDialog(self.main_window)
            dialog.setWindowTitle("⚙️ الإعدادات السريعة")
            dialog.setFixedSize(400, 300)
            dialog.setModal(True)

            # تطبيق نمط النافذة
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1e293b, stop:1 #334155);
                    color: white;
                    border-radius: 10px;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
                QCheckBox, QLabel {
                    color: white;
                    font-size: 12px;
                }
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #60a5fa, stop:1 #3b82f6);
                }
            """)

            layout = QVBoxLayout(dialog)

            # مجموعة إعدادات العرض
            display_group = QGroupBox("🖥️ إعدادات العرض")
            display_layout = QVBoxLayout(display_group)

            # خيار الوضع المظلم
            dark_mode_cb = QCheckBox("🌙 الوضع المظلم")
            dark_mode_cb.setChecked(True)
            display_layout.addWidget(dark_mode_cb)

            # خيار إظهار الأيقونات
            show_icons_cb = QCheckBox("🎨 إظهار الأيقونات")
            show_icons_cb.setChecked(True)
            display_layout.addWidget(show_icons_cb)

            layout.addWidget(display_group)

            # مجموعة إعدادات الأداء
            performance_group = QGroupBox("⚡ إعدادات الأداء")
            performance_layout = QVBoxLayout(performance_group)

            # سرعة التحديث
            refresh_label = QLabel("🔄 سرعة التحديث:")
            performance_layout.addWidget(refresh_label)

            refresh_slider = QSlider(Qt.Horizontal)
            refresh_slider.setRange(1, 10)
            refresh_slider.setValue(5)
            performance_layout.addWidget(refresh_slider)

            layout.addWidget(performance_group)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            save_btn = QPushButton("💾 حفظ")
            save_btn.clicked.connect(lambda: self.save_quick_settings(dialog, dark_mode_cb, show_icons_cb, refresh_slider))
            buttons_layout.addWidget(save_btn)

            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)

            # إظهار النافذة
            dialog.exec_()

        except Exception as e:
            print(f"❌ خطأ في فتح نافذة الإعدادات: {str(e)}")

    def save_quick_settings(self, dialog, dark_mode_cb, show_icons_cb, refresh_slider):
        """حفظ الإعدادات السريعة"""
        try:
            # حفظ الإعدادات
            settings = {
                'dark_mode': dark_mode_cb.isChecked(),
                'show_icons': show_icons_cb.isChecked(),
                'refresh_speed': refresh_slider.value()
            }

            # تطبيق الإعدادات
            if settings['refresh_speed'] != 5:  # القيمة الافتراضية
                new_interval = 1000 * settings['refresh_speed']  # تحويل إلى ميلي ثانية
                if hasattr(self, 'update_timer'):
                    self.update_timer.start(new_interval)

            # إظهار رسالة نجاح
            self.show_status_message("✅ تم حفظ الإعدادات", 2000)

            dialog.accept()
            print(f"✅ تم حفظ الإعدادات: {settings}")

        except Exception as e:
            print(f"❌ خطأ في حفظ الإعدادات: {str(e)}")

    def toggle_advanced_mode(self):
        """🌟 تبديل الوضع المتقدم مع ميزات حقيقية"""
        try:
            if self.advanced_mode_btn.isChecked():
                # تفعيل الوضع المتقدم
                self.network_status.setText("🌟 الوضع: متقدم")
                self.advanced_mode_btn.setToolTip("🌟 إيقاف الوضع المتقدم")

                # تسريع التحديث في الوضع المتقدم
                if hasattr(self, 'update_timer'):
                    self.update_timer.start(2000)  # كل ثانيتين

                # تفعيل ميزات متقدمة
                self.enable_advanced_features()

                # إظهار رسالة تأكيد
                self.show_status_message("🌟 تم تفعيل الوضع المتقدم", 3000)

            else:
                # إيقاف الوضع المتقدم
                self.network_status.setText("🌐 الوضع: عادي")
                self.advanced_mode_btn.setToolTip("🌟 تشغيل الوضع المتقدم")

                # إعادة التحديث للوضع العادي
                if hasattr(self, 'update_timer'):
                    self.update_timer.start(10000)  # كل 10 ثوان

                # إيقاف الميزات المتقدمة
                self.disable_advanced_features()

                # إظهار رسالة تأكيد
                self.show_status_message("📱 تم تفعيل الوضع العادي", 3000)

            print(f"✅ تم تبديل الوضع: {'متقدم' if self.advanced_mode_btn.isChecked() else 'عادي'}")

        except Exception as e:
            print(f"❌ خطأ في تبديل الوضع المتقدم: {str(e)}")

    def enable_advanced_features(self):
        """تفعيل الميزات المتقدمة"""
        try:
            # تفعيل مراقبة الأداء المتقدمة
            if hasattr(self.main_window, 'enable_performance_monitoring'):
                self.main_window.enable_performance_monitoring()

            # تفعيل التحديث التلقائي السريع
            if hasattr(self, 'datetime_timer'):
                self.datetime_timer.start(500)  # تحديث أسرع للوقت

            # تغيير ألوان المؤشرات للوضع المتقدم
            self.smart_performance.setStyleSheet(self.smart_performance.styleSheet() + """
                QLabel {
                    border: 2px solid #10b981;
                    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
                }
            """)

            print("✅ تم تفعيل الميزات المتقدمة")

        except Exception as e:
            print(f"❌ خطأ في تفعيل الميزات المتقدمة: {str(e)}")

    def disable_advanced_features(self):
        """إيقاف الميزات المتقدمة"""
        try:
            # إيقاف مراقبة الأداء المتقدمة
            if hasattr(self.main_window, 'disable_performance_monitoring'):
                self.main_window.disable_performance_monitoring()

            # إعادة التحديث العادي للوقت
            if hasattr(self, 'datetime_timer'):
                self.datetime_timer.start(1000)  # تحديث عادي

            # إعادة الألوان العادية
            self.smart_performance.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:0.2 rgba(255, 255, 255, 0.20),
                        stop:0.4 rgba(255, 255, 255, 0.25),
                        stop:0.6 rgba(255, 255, 255, 0.20),
                        stop:0.8 rgba(255, 255, 255, 0.15),
                        stop:1 rgba(255, 255, 255, 0.10));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 15px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)

            print("✅ تم إيقاف الميزات المتقدمة")

        except Exception as e:
            print(f"❌ خطأ في إيقاف الميزات المتقدمة: {str(e)}")

    def show_status_message(self, message, duration=2000):
        """إظهار رسالة في شريط الحالة"""
        try:
            if hasattr(self.main_window, 'status_message_label'):
                original_text = self.main_window.status_message_label.text()
                self.main_window.status_message_label.setText(message)
                QTimer.singleShot(duration, lambda: self.main_window.status_message_label.setText(original_text))
        except Exception as e:
            print(f"❌ خطأ في إظهار رسالة الحالة: {str(e)}")

    def open_main_settings(self):
        """فتح نافذة الإعدادات الرئيسية للبرنامج"""
        try:
            # تأثير بصري
            original_text = self.security_status.text()
            self.security_status.setText("⚙️ فتح الإعدادات...")

            # محاولة فتح نافذة الإعدادات الرئيسية
            try:
                # التحقق من وجود قسم الإعدادات في التبويبات
                if hasattr(self.main_window, 'tabs'):
                    settings_tab_index = -1
                    for i in range(self.main_window.tabs.count()):
                        tab_text = self.main_window.tabs.tabText(i)
                        if "إعدادات" in tab_text or "الإعدادات" in tab_text:
                            settings_tab_index = i
                            break

                    if settings_tab_index >= 0:
                        # الانتقال إلى تبويب الإعدادات
                        self.main_window.tabs.setCurrentIndex(settings_tab_index)
                        self.show_status_message("✅ تم فتح قسم الإعدادات", 2000)
                    else:
                        # فتح نافذة الإعدادات السريعة كبديل
                        self.open_quick_settings_dialog()
                        self.show_status_message("✅ تم فتح الإعدادات السريعة", 2000)
                else:
                    # فتح نافذة الإعدادات السريعة
                    self.open_quick_settings_dialog()

            except Exception as settings_error:
                print(f"خطأ في فتح الإعدادات الرئيسية: {settings_error}")
                # فتح نافذة الإعدادات السريعة كبديل
                self.open_quick_settings_dialog()

            # إعادة النص الأصلي
            QTimer.singleShot(2000, lambda: self.security_status.setText(original_text))

            print("⚙️ تم فتح الإعدادات")

        except Exception as e:
            print(f"❌ خطأ في فتح الإعدادات: {str(e)}")

    def get_database_stats(self, session):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            stats = {}

            # محاولة الحصول على عدد العملاء
            try:
                from database import Client
                stats['clients'] = session.query(Client).count()
            except Exception:
                stats['clients'] = 0

            # محاولة الحصول على عدد الموردين
            try:
                from database import Supplier
                stats['suppliers'] = session.query(Supplier).count()
            except Exception:
                stats['suppliers'] = 0

            # محاولة الحصول على عدد الموظفين
            try:
                from database import Employee
                stats['employees'] = session.query(Employee).count()
            except Exception:
                stats['employees'] = 0

            # محاولة الحصول على عدد المشاريع
            try:
                from database import Project
                stats['projects'] = session.query(Project).count()
            except Exception:
                stats['projects'] = 0

            # محاولة الحصول على عدد المصروفات
            try:
                from database import Expense
                stats['expenses'] = session.query(Expense).count()
            except Exception:
                stats['expenses'] = 0

            # محاولة الحصول على عدد الإيرادات
            try:
                from database import Revenue
                stats['revenues'] = session.query(Revenue).count()
            except Exception:
                stats['revenues'] = 0

            # حساب إجمالي السجلات
            stats['total_records'] = (
                stats['clients'] + stats['suppliers'] + stats['employees'] +
                stats['projects'] + stats['expenses'] + stats['revenues']
            )

            return stats

        except Exception as e:
            print(f"❌ خطأ في الحصول على إحصائيات قاعدة البيانات: {str(e)}")
            return None

    def get_system_uptime(self):
        """الحصول على وقت تشغيل البرنامج"""
        try:
            # إذا لم يكن وقت البداية محفوظاً، احفظه الآن
            if not hasattr(self, 'start_time'):
                self.start_time = datetime.datetime.now()

            # حساب الفرق الزمني
            uptime = datetime.datetime.now() - self.start_time

            # تنسيق الوقت
            hours = uptime.seconds // 3600
            minutes = (uptime.seconds % 3600) // 60
            seconds = uptime.seconds % 60

            if uptime.days > 0:
                return f"{uptime.days} يوم، {hours:02d}:{minutes:02d}:{seconds:02d}"
            else:
                return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

        except Exception as e:
            print(f"❌ خطأ في حساب وقت التشغيل: {str(e)}")
            return "غير معروف"



    def initialize_current_section(self):
        """تهيئة القسم الحالي في شريط الحالة عند بدء التشغيل"""
        try:
            # التحقق من وجود التبويبات
            if hasattr(self.main_window, 'tabs') and self.main_window.tabs.count() > 0:
                # الحصول على التبويب الحالي
                current_index = self.main_window.tabs.currentIndex()
                if current_index >= 0:
                    current_tab_name = self.main_window.tabs.tabText(current_index)
                    self.update_current_section(current_tab_name)
                    print(f"✅ تم تهيئة شريط الحالة بالقسم: {current_tab_name}")
                else:
                    # إذا لم يكن هناك تبويب محدد، استخدم لوحة المعلومات
                    self.update_current_section("لوحة المعلومات")
                    print("✅ تم تهيئة شريط الحالة بالقسم الافتراضي: لوحة المعلومات")
            else:
                # إذا لم تكن التبويبات جاهزة بعد، استخدم لوحة المعلومات كافتراضي
                self.update_current_section("لوحة المعلومات")
                print("✅ تم تهيئة شريط الحالة بالقسم الافتراضي: لوحة المعلومات (التبويبات غير جاهزة)")

        except Exception as e:
            print(f"❌ خطأ في تهيئة القسم الحالي: {str(e)}")
            # في حالة الخطأ، استخدم لوحة المعلومات كافتراضي
            try:
                self.update_current_section("لوحة المعلومات")
            except:
                pass

    def check_security_status(self):
        """فحص حالة الأمان الشامل"""
        try:
            import os
            security_info = {
                'database': 'غير معروف',
                'files': 'غير معروف',
                'network': 'غير معروف',
                'user': 'غير معروف',
                'level': 'low',
                'level_text': 'ضعيف'
            }

            security_score = 0

            # فحص قاعدة البيانات (أهمية عالية - 35 نقطة)
            if os.path.exists("database.db"):
                file_size = os.path.getsize("database.db")
                if file_size > 0:
                    security_info['database'] = "محمية ✅"
                    security_score += 35  # زيادة النقاط لأهمية قاعدة البيانات
                else:
                    security_info['database'] = "فارغة ⚠️"
                    security_score += 15
            else:
                security_info['database'] = "غير موجودة ❌"

            # فحص الملفات المهمة
            important_files = ['main.py', 'database.py', 'ui/']
            existing_files = 0
            for file_path in important_files:
                if os.path.exists(file_path):
                    existing_files += 1

            if existing_files == len(important_files):
                security_info['files'] = "سليمة ✅"
                security_score += 35  # زيادة النقاط لأهمية الملفات الأساسية
            elif existing_files > 0:
                security_info['files'] = "ناقصة ⚠️"
                security_score += 20
            else:
                security_info['files'] = "مفقودة ❌"

            # فحص الشبكة (للمعلومات فقط - لا يؤثر على الأمان المحلي)
            try:
                import socket
                socket.create_connection(("8.8.8.8", 53), timeout=2)
                security_info['network'] = "متصلة 📶"
            except:
                security_info['network'] = "غير متصلة 📵"

            # فحص إضافي: سلامة مجلد البرنامج (بديل عن نقاط الشبكة)
            try:
                import os
                program_files = ['main.py', 'database.py', 'ui/']
                secure_files = 0
                for file_path in program_files:
                    if os.path.exists(file_path):
                        secure_files += 1

                if secure_files == len(program_files):
                    security_info['program_integrity'] = "سليم ✅"
                    security_score += 25  # نقاط بديلة عن الشبكة
                elif secure_files > 1:
                    security_info['program_integrity'] = "جيد ⚠️"
                    security_score += 15
                else:
                    security_info['program_integrity'] = "تالف ❌"

            except Exception:
                security_info['program_integrity'] = "غير معروف ❓"
                security_score += 10

            # فحص المستخدم (أهمية متوسطة - 30 نقطة)
            if hasattr(self.main_window, 'user') and self.main_window.user:
                if self.main_window.user.role == 'admin':
                    security_info['user'] = "مدير ✅"
                    security_score += 30
                else:
                    security_info['user'] = "مستخدم عادي ⚠️"
                    security_score += 20
            else:
                security_info['user'] = "غير محدد ❌"
                security_score += 10  # نقاط أساسية حتى لو لم يكن هناك مستخدم محدد

            # تحديد مستوى الأمان
            if security_score >= 80:
                security_info['level'] = 'high'
                security_info['level_text'] = 'ممتاز'
            elif security_score >= 50:
                security_info['level'] = 'medium'
                security_info['level_text'] = 'جيد'
            else:
                security_info['level'] = 'low'
                security_info['level_text'] = 'ضعيف'

            return security_info

        except Exception as e:
            print(f"❌ خطأ في فحص الأمان: {str(e)}")
            return {
                'database': 'خطأ',
                'files': 'خطأ',
                'network': 'خطأ',
                'user': 'خطأ',
                'level': 'low',
                'level_text': 'غير معروف'
            }

    def get_section_icon(self, section_name):
        """🎯 الحصول على أيقونة مناسبة للقسم بدقة عالية"""
        # تنظيف اسم القسم
        clean_name = section_name.strip()

        # قاموس الأيقونات حسب اسم القسم - متطابق مع الشريط الأفقي
        section_icons = {
            # الأقسام الرئيسية - نفس أيقونات الشريط الأفقي المحدثة
            "لوحة المعلومات": "📊",
            "لوحة التحكم": "📊",
            "الصفحة الرئيسية": "📊",

            # أقسام العملاء - نفس أيقونة الشريط الأفقي
            "إدارة العملاء": "🤝",
            "إضافة عميل جديد": "🤝",
            "إضافة عميل": "🤝",
            "تعديل بيانات العميل": "🤝",
            "تعديل عميل": "🤝",
            "حذف عميل": "🤝",
            "بحث في العملاء": "🤝",
            "بحث العملاء": "🤝",
            "قائمة العملاء": "🤝",
            "تفاصيل العميل": "🤝",
            "العملاء": "🤝",

            # أقسام الموردين - نفس أيقونة الشريط الأفقي
            "إدارة الموردين": "🚛",
            "إضافة مورد": "🚛",
            "تعديل مورد": "🚛",
            "قائمة الموردين": "🚛",
            "الموردين": "🚛",

            # أقسام الموظفين/العمال - نفس أيقونة الشريط الأفقي
            "إدارة الموظفين": "👷‍♂️",
            "إدارة العمال": "👷‍♂️",
            "إضافة موظف": "👷‍♂️",
            "تعديل موظف": "👷‍♂️",
            "قائمة الموظفين": "👷‍♂️",
            "الموظفين": "👷‍♂️",
            "العمال": "👷‍♂️",

            # أقسام المشاريع - نفس أيقونة الشريط الأفقي
            "إدارة المشاريع": "🏗️",
            "إضافة مشروع": "🏗️",
            "تعديل مشروع": "🏗️",
            "قائمة المشاريع": "🏗️",
            "المشاريع": "🏗️",

            # أقسام المخازن - نفس أيقونة الشريط الأفقي
            "إدارة المخازن": "🏪",
            "إضافة مخزن": "🏪",
            "جرد المخزن": "🏪",
            "حركة المخزن": "🏪",
            "المخازن": "🏪",
            "المخزون": "🏪",
            "إدارة المخزون": "🏪",

            # أقسام المصروفات - نفس أيقونة الشريط الأفقي
            "إدارة المصروفات": "💰",
            "إضافة مصروف جديد": "💰",
            "إضافة مصروف": "💰",
            "تصنيف المصروفات": "💰",
            "تقرير المصروفات": "💰",
            "المصروفات": "💰",

            # أقسام الإيرادات - نفس أيقونة الشريط الأفقي
            "إدارة الإيرادات": "💵",
            "الإيرادات": "💵",

            # أقسام الفواتير - نفس أيقونة الشريط الأفقي
            "إدارة الفواتير": "📋",
            "إنشاء فاتورة جديدة": "📋",
            "إنشاء فاتورة": "📋",
            "طباعة فاتورة": "📋",
            "فواتير مستحقة": "📋",
            "فواتير مدفوعة": "📋",
            "فواتير ملغاة": "📋",
            "تقرير الفواتير": "📋",
            "الفواتير": "📋",

            # أقسام الأقساط - نفس أيقونة الشريط الأفقي
            "إدارة الأقساط": "🏦",
            "إضافة قسط جديد": "🏦",
            "إضافة قسط": "🏦",
            "تعديل قسط": "🏦",
            "حذف قسط": "🏦",
            "بحث في الأقساط": "🏦",
            "بحث الأقساط": "🏦",
            "قائمة الأقساط": "🏦",
            "تفاصيل القسط": "🏦",
            "الأقساط": "🏦",
            "أقساط معلقة": "🏦",
            "أقساط مسددة": "🏦",
            "أقساط متأخرة": "🏦",
            "تقرير الأقساط": "🏦",

            # أقسام الإشعارات - نفس أيقونة الشريط الأفقي
            "الإشعارات": "🔔",
            "إدارة الإشعارات": "🔔",

            # أقسام التقارير - نفس أيقونة الشريط الأفقي المحدثة
            "تقارير العملاء": "📈",
            "تقارير الفواتير": "📈",
            "تقارير المصروفات": "📈",
            "تقارير مالية": "📈",
            "تقارير شهرية": "📈",
            "تقارير سنوية": "📈",
            "التقارير": "📈",

            # أقسام الإعدادات - نفس أيقونة الشريط الأفقي
            "إعدادات النظام": "⚙️",
            "إعدادات المستخدم": "⚙️",
            "إعدادات الأمان": "⚙️",
            "إعدادات النسخ الاحتياطي": "⚙️",
            "الإعدادات": "⚙️",

            # أقسام أخرى
            "النسخ الاحتياطي": "💾",
            "استعادة النسخة": "🔄",
            "الاستعادة": "🔄",
            "التصدير": "📤",
            "الاستيراد": "📥",
            "إدارة المستخدمين": "👤",
            "إضافة مستخدم": "👤",
            "صلاحيات المستخدمين": "🔑",
            "المستخدمين": "👤",
            "الأمان": "🔐",
            "قاعدة البيانات": "🗄️",
            "إعدادات الشبكة": "🌐",
            "الشبكة": "🌐",
            "الطباعة": "🖨️",
            "البريد الإلكتروني": "📧",
            "المساعدة": "❓",
            "حول البرنامج": "ℹ️"
        }

        # البحث بالتطابق التام أولاً
        if clean_name in section_icons:
            return section_icons[clean_name]

        # البحث بالتطابق الجزئي - مرتب حسب الطول (الأطول أولاً لتجنب التداخل)
        sorted_keys = sorted(section_icons.keys(), key=len, reverse=True)
        for key in sorted_keys:
            if key in clean_name:
                return section_icons[key]

        # أيقونة افتراضية إذا لم يتم العثور على تطابق
        return "📍"

    def update_current_section(self, section_name):
        """📍 تحديث مؤشر القسم الحالي مع أيقونة مناسبة"""
        try:
            if hasattr(self.main_window, 'status_message_label') and self.main_window.status_message_label:
                # الحصول على الأيقونة المناسبة للقسم
                section_icon = self.get_section_icon(section_name)

                # تحديث النص مع الأيقونة المناسبة (نظام موحد لجميع الأقسام)
                self.main_window.status_message_label.setText(f"{section_icon} القسم الحالي: {section_name}")

                # معلومات تشخيصية لمساعدة المطور
                self.debug_section_icon_selection(section_name, section_icon)

                # طباعة رسالة مبسطة فقط عند التغيير الفعلي
                if not hasattr(self, '_last_section') or self._last_section != section_name:
                    print(f"✅ تم تحديث القسم الحالي: {section_icon} {section_name}")

                    self._last_section = section_name

        except Exception as e:
            print(f"❌ خطأ في تحديث مؤشر القسم الحالي: {str(e)}")

    def debug_section_icon_selection(self, section_name, selected_icon):
        """🔍 معلومات تشخيصية لاختيار الأيقونة"""
        try:
            # قائمة بجميع الأيقونات المحتملة لهذا القسم
            clean_name = section_name.strip()
            section_icons = {
                "لوحة المعلومات": "📊", "لوحة التحكم": "📊", "الصفحة الرئيسية": "📊",
                "إدارة العملاء": "🤝", "إضافة عميل جديد": "🤝", "إضافة عميل": "🤝",
                "تعديل بيانات العميل": "🤝", "تعديل عميل": "🤝", "حذف عميل": "🤝",
                "بحث في العملاء": "🤝", "بحث العملاء": "🤝", "قائمة العملاء": "🤝",
                "تفاصيل العميل": "🤝", "العملاء": "🤝",
                "إدارة الموردين": "🚛", "إضافة مورد": "🚛", "تعديل مورد": "🚛",
                "قائمة الموردين": "🚛", "الموردين": "🚛",
                "إدارة الموظفين": "👷‍♂️", "إضافة موظف": "👷‍♂️", "تعديل موظف": "👷‍♂️",
                "قائمة الموظفين": "👷‍♂️", "الموظفين": "👷‍♂️", "العمال": "👷‍♂️",
                "إدارة المشاريع": "🏗️", "إضافة مشروع": "🏗️", "تعديل مشروع": "🏗️",
                "قائمة المشاريع": "🏗️", "المشاريع": "🏗️",
                "إدارة المخازن": "🏪", "إضافة مخزن": "🏪", "جرد المخزن": "🏪",
                "حركة المخزن": "🏪", "المخازن": "🏪",
                "إدارة المصروفات": "💰", "المصروفات": "💰",

                # الأقسام المفقودة
                "الإيرادات": "💵", "إدارة الإيرادات": "💵", "إضافة إيراد": "💵",
                "الفواتير": "📋", "إدارة الفواتير": "📋", "إضافة فاتورة": "📋",
                "الأقساط": "🏦", "إدارة الأقساط": "🏦", "إضافة قسط": "🏦",
                "الإشعارات": "🔔", "إدارة الإشعارات": "🔔", "عرض الإشعارات": "🔔",
                "التقارير": "📈", "إدارة التقارير": "📈", "عرض التقارير": "📈",
                "المبيعات": "💳", "إدارة المبيعات": "💳", "إضافة مبيعة": "💳", "قائمة المبيعات": "💳",
                "المشتريات": "🛒", "إدارة المشتريات": "🛒", "إضافة مشتريات": "🛒", "قائمة المشتريات": "🛒",
                "العقارات": "🏠", "إدارة العقارات": "🏠", "إضافة عقار": "🏠", "قائمة العقارات": "🏠",
                "الإعدادات": "⚙️", "إعدادات البرنامج": "⚙️", "الضبط": "⚙️"
            }

            # البحث عن التطابقات المحتملة
            matches = []

            # تطابق تام
            if clean_name in section_icons:
                matches.append(f"✅ تطابق تام: '{clean_name}' → {section_icons[clean_name]}")

            # تطابق جزئي
            sorted_keys = sorted(section_icons.keys(), key=len, reverse=True)
            for key in sorted_keys:
                if key in clean_name and key != clean_name:
                    matches.append(f"🔍 تطابق جزئي: '{key}' في '{clean_name}' → {section_icons[key]}")

            # طباعة رسالة مبسطة فقط للأقسام غير المعروفة
            if not matches and not hasattr(self, '_warned_sections'):
                self._warned_sections = set()

            if not matches and section_name not in getattr(self, '_warned_sections', set()):
                print(f"⚠️ قسم جديد: '{section_name}' - أيقونة افتراضية: {selected_icon}")
                if hasattr(self, '_warned_sections'):
                    self._warned_sections.add(section_name)

        except Exception as e:
            print(f"❌ خطأ في التشخيص: {str(e)}")
